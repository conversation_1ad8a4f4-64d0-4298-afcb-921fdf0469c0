import sqlalchemy as sa
import pandas as pd
import urllib
import pyodbc
from sqlalchemy import create_engine

server = 'localhost'
database = 'ctCECILIO_ERNESTO'
username = 'sa'
password = 'Aioria86-'
driver = 'ODBC Driver 17 for SQL Server'

# pyodbc
conn = f'DRIVER={driver};SERVER={server};DATABASE={database};UID={username};PWD={password};'
engine = create_engine(f'mssql+pyodbc://@{server}/{database}?trusted_connection=yes&driver={driver}')

Empleados = pd.read_sql_query('SELECT * FROM dbo.nom10001', engine)
print(Empleados.head())